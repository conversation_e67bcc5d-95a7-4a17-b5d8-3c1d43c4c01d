name: Update Beta Tag

on:
  release:
    types: [published]

jobs:
  update-beta-tag:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4

      - name: Update beta tag
        run: |
          # Get the current release version
          VERSION=${GITHUB_REF#refs/tags/}

          # Update the beta tag to point to this release
          git config user.name github-actions[bot]
          git config user.email github-actions[bot]@users.noreply.github.com
          git tag -fa beta -m "Update beta tag to ${VERSION}"
          git push origin beta --force
