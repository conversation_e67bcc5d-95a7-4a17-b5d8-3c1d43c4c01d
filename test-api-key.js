// Simple script to test Anthropic API key
const fetch = require('node-fetch');

async function testApiKey() {
  const apiKey = process.env.ANTHROPIC_API_KEY;
  
  if (!apiKey) {
    console.error('ANTHROPIC_API_KEY environment variable is not set');
    process.exit(1);
  }
  
  console.log('API key is set:', apiKey.substring(0, 10) + '...');
  
  try {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: 'claude-3-haiku-20240307',
        max_tokens: 10,
        messages: [
          { role: 'user', content: 'Hello, world!' }
        ]
      })
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('API key is valid! Response:', data);
    } else {
      console.error('API key validation failed:', data);
    }
  } catch (error) {
    console.error('Error testing API key:', error);
  }
}

testApiKey();