name: "Claude Code Action Official"
description: "General-purpose Claude agent for GitHub PRs and issues. Can answer questions and implement code changes."
branding:
  icon: "at-sign"
  color: "orange"

inputs:
  trigger_phrase:
    description: "The trigger phrase to look for in comments or issue body"
    required: false
    default: "@claude"
  assignee_trigger:
    description: "The assignee username that triggers the action (e.g. @claude)"
    required: false
  base_branch:
    description: "The branch to use as the base/source when creating new branches (defaults to repository default branch)"
    required: false

  # Claude Code configuration
  model:
    description: "Model to use (provider-specific format required for Bedrock/Vertex)"
    required: false
  anthropic_model:
    description: "DEPRECATED: Use 'model' instead. Model to use (provider-specific format required for Bedrock/Vertex)"
    required: false
  allowed_tools:
    description: "Additional tools for <PERSON> to use (the base GitHub tools will always be included)"
    required: false
    default: ""
  disallowed_tools:
    description: "Tools that <PERSON> should never use"
    required: false
    default: ""
  custom_instructions:
    description: "Additional custom instructions to include in the prompt for <PERSON>"
    required: false
    default: ""
  direct_prompt:
    description: "Direct instruction for <PERSON> (bypasses normal trigger detection)"
    required: false
    default: ""
  mcp_config:
    description: "Additional MCP configuration (JSON string) that merges with the built-in GitHub MCP servers"
  claude_env:
    description: "Custom environment variables to pass to Claude Code execution (YAML format)"
    required: false
    default: ""

  # Auth configuration
  anthropic_api_key:
    description: "Anthropic API key (required for direct API, not needed for Bedrock/Vertex)"
    required: false
  github_token:
    description: "GitHub token with repo and pull request permissions (optional if using GitHub App)"
    required: false
  use_bedrock:
    description: "Use Amazon Bedrock with OIDC authentication instead of direct Anthropic API"
    required: false
    default: "false"
  use_vertex:
    description: "Use Google Vertex AI with OIDC authentication instead of direct Anthropic API"
    required: false
    default: "false"

  max_turns:
    description: "Maximum number of conversation turns"
    required: false
    default: ""
  timeout_minutes:
    description: "Timeout in minutes for execution"
    required: false
    default: "30"

outputs:
  execution_file:
    description: "Path to the Claude Code execution output file"
    value: ${{ steps.claude-code.outputs.execution_file }}

runs:
  using: "composite"
  steps:
    - name: Install Bun
      uses: oven-sh/setup-bun@735343b667d3e6f658f44d0eca948eb6282f2b76 # https://github.com/oven-sh/setup-bun/releases/tag/v2.0.2
      with:
        bun-version: 1.2.11

    - name: Install Dependencies
      shell: bash
      run: |
        cd ${GITHUB_ACTION_PATH}
        bun install

    - name: Prepare action
      id: prepare
      shell: bash
      run: |
        bun run ${GITHUB_ACTION_PATH}/src/entrypoints/prepare.ts
      env:
        TRIGGER_PHRASE: ${{ inputs.trigger_phrase }}
        ASSIGNEE_TRIGGER: ${{ inputs.assignee_trigger }}
        BASE_BRANCH: ${{ inputs.base_branch }}
        ALLOWED_TOOLS: ${{ inputs.allowed_tools }}
        DISALLOWED_TOOLS: ${{ inputs.disallowed_tools }}
        CUSTOM_INSTRUCTIONS: ${{ inputs.custom_instructions }}
        DIRECT_PROMPT: ${{ inputs.direct_prompt }}
        MCP_CONFIG: ${{ inputs.mcp_config }}
        OVERRIDE_GITHUB_TOKEN: ${{ inputs.github_token }}
        GITHUB_RUN_ID: ${{ github.run_id }}

    - name: Run Claude Code
      id: claude-code
      if: steps.prepare.outputs.contains_trigger == 'true'
      uses: anthropics/claude-code-base-action@3933d9c3c25f2b027392a370be6f0bbd5989b271 # v0.0.18
      with:
        prompt_file: ${{ runner.temp }}/claude-prompts/claude-prompt.txt
        allowed_tools: ${{ env.ALLOWED_TOOLS }}
        disallowed_tools: ${{ env.DISALLOWED_TOOLS }}
        timeout_minutes: ${{ inputs.timeout_minutes }}
        max_turns: ${{ inputs.max_turns }}
        model: ${{ inputs.model || inputs.anthropic_model }}
        mcp_config: ${{ steps.prepare.outputs.mcp_config }}
        use_bedrock: ${{ inputs.use_bedrock }}
        use_vertex: ${{ inputs.use_vertex }}
        anthropic_api_key: ${{ inputs.anthropic_api_key }}
        claude_env: ${{ inputs.claude_env }}
      env:
        # Model configuration
        ANTHROPIC_MODEL: ${{ inputs.model || inputs.anthropic_model }}
        GITHUB_TOKEN: ${{ steps.prepare.outputs.GITHUB_TOKEN }}

        # Provider configuration
        ANTHROPIC_BASE_URL: ${{ env.ANTHROPIC_BASE_URL }}

        # AWS configuration
        AWS_REGION: ${{ env.AWS_REGION }}
        AWS_ACCESS_KEY_ID: ${{ env.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ env.AWS_SECRET_ACCESS_KEY }}
        AWS_SESSION_TOKEN: ${{ env.AWS_SESSION_TOKEN }}
        ANTHROPIC_BEDROCK_BASE_URL: ${{ env.ANTHROPIC_BEDROCK_BASE_URL }}

        # GCP configuration
        ANTHROPIC_VERTEX_PROJECT_ID: ${{ env.ANTHROPIC_VERTEX_PROJECT_ID }}
        CLOUD_ML_REGION: ${{ env.CLOUD_ML_REGION }}
        GOOGLE_APPLICATION_CREDENTIALS: ${{ env.GOOGLE_APPLICATION_CREDENTIALS }}
        ANTHROPIC_VERTEX_BASE_URL: ${{ env.ANTHROPIC_VERTEX_BASE_URL }}

        # Model-specific regions for Vertex
        VERTEX_REGION_CLAUDE_3_5_HAIKU: ${{ env.VERTEX_REGION_CLAUDE_3_5_HAIKU }}
        VERTEX_REGION_CLAUDE_3_5_SONNET: ${{ env.VERTEX_REGION_CLAUDE_3_5_SONNET }}
        VERTEX_REGION_CLAUDE_3_7_SONNET: ${{ env.VERTEX_REGION_CLAUDE_3_7_SONNET }}

    - name: Update comment with job link
      if: steps.prepare.outputs.contains_trigger == 'true' && steps.prepare.outputs.claude_comment_id && always()
      shell: bash
      run: |
        bun run ${GITHUB_ACTION_PATH}/src/entrypoints/update-comment-link.ts
      env:
        REPOSITORY: ${{ github.repository }}
        PR_NUMBER: ${{ github.event.issue.number || github.event.pull_request.number }}
        CLAUDE_COMMENT_ID: ${{ steps.prepare.outputs.claude_comment_id }}
        GITHUB_RUN_ID: ${{ github.run_id }}
        GITHUB_TOKEN: ${{ steps.prepare.outputs.GITHUB_TOKEN }}
        GITHUB_EVENT_NAME: ${{ github.event_name }}
        TRIGGER_COMMENT_ID: ${{ github.event.comment.id }}
        CLAUDE_BRANCH: ${{ steps.prepare.outputs.CLAUDE_BRANCH }}
        IS_PR: ${{ github.event.issue.pull_request != null || github.event_name == 'pull_request_review_comment' }}
        BASE_BRANCH: ${{ steps.prepare.outputs.BASE_BRANCH }}
        CLAUDE_SUCCESS: ${{ steps.claude-code.outputs.conclusion == 'success' }}
        OUTPUT_FILE: ${{ steps.claude-code.outputs.execution_file || '' }}
        TRIGGER_USERNAME: ${{ github.event.comment.user.login || github.event.issue.user.login || github.event.pull_request.user.login || github.event.sender.login || github.triggering_actor || github.actor || '' }}
        PREPARE_SUCCESS: ${{ steps.prepare.outcome == 'success' }}
        PREPARE_ERROR: ${{ steps.prepare.outputs.prepare_error || '' }}

    - name: Display Claude Code Report
      if: steps.prepare.outputs.contains_trigger == 'true' && steps.claude-code.outputs.execution_file != ''
      shell: bash
      run: |
        echo "## Claude Code Report" >> $GITHUB_STEP_SUMMARY
        echo '```json' >> $GITHUB_STEP_SUMMARY
        cat "${{ steps.claude-code.outputs.execution_file }}" >> $GITHUB_STEP_SUMMARY
        echo '```' >> $GITHUB_STEP_SUMMARY

    - name: Revoke app token
      if: always() && inputs.github_token == ''
      shell: bash
      run: |
        curl -L \
          -X DELETE \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: Bearer ${{ steps.prepare.outputs.GITHUB_TOKEN }}" \
          -H "X-GitHub-Api-Version: 2022-11-28" \
          ${GITHUB_API_URL:-https://api.github.com}/installation/token
