# First, backup any local changes if needed
# git stash  # Uncomment if you have changes you want to save

# Check current remotes
git remote -v

# Remove the current origin
git remote remove origin

# Add your fork as the new origin
git remote add origin https://github.com/harjothkhara/claude-code-action.git

# Fetch the latest from your fork
git fetch origin

# Reset your local main branch to match your fork's main branch
git checkout main
git reset --hard origin/main

# Verify the new remote is set up correctly
git remote -v