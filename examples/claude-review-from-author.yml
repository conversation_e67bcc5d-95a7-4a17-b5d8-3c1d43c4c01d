name: <PERSON> Review - Specific Authors

on:
  pull_request:
    types: [opened, synchronize]

jobs:
  review-by-author:
    # Only run for PRs from specific authors
    if: |
      github.event.pull_request.user.login == 'developer1' ||
      github.event.pull_request.user.login == 'developer2' ||
      github.event.pull_request.user.login == 'external-contributor'
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: read
      id-token: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Review PR from Specific Author
        uses: anthropics/claude-code-action@beta
        with:
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          timeout_minutes: "60"
          direct_prompt: |
            Please provide a thorough review of this pull request.

            Since this is from a specific author that requires careful review,
            please pay extra attention to:
            - Adherence to project coding standards
            - Proper error handling
            - Security best practices
            - Test coverage
            - Documentation

            Provide detailed feedback and suggestions for improvement.
