name: Claude <PERSON> Review

on:
  pull_request:
    types: [opened, synchronize]

jobs:
  auto-review:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: read
      id-token: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Automatic PR Review
        uses: anthropics/claude-code-action@beta
        with:
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          timeout_minutes: "60"
          direct_prompt: |
            Please review this pull request and provide comprehensive feedback.

            Focus on:
            - Code quality and best practices
            - Potential bugs or issues
            - Performance considerations
            - Security implications
            - Test coverage
            - Documentation updates if needed

            Provide constructive feedback with specific suggestions for improvement.
            Use inline comments to highlight specific areas of concern.
          # allowed_tools: "mcp__github__create_pending_pull_request_review,mcp__github__add_pull_request_review_comment_to_pending_review,mcp__github__submit_pending_pull_request_review,mcp__github__get_pull_request_diff"
