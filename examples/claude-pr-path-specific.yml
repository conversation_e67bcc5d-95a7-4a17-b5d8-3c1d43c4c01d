name: <PERSON> - Path Specific

on:
  pull_request:
    types: [opened, synchronize]
    paths:
      # Only run when specific paths are modified
      - "src/**/*.js"
      - "src/**/*.ts"
      - "api/**/*.py"
      # You can add more specific patterns as needed

jobs:
  claude-review-paths:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: read
      id-token: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Claude Code Review
        uses: anthropics/claude-code-action@beta
        with:
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          timeout_minutes: "60"
          direct_prompt: |
            Please review this pull request focusing on the changed files.
            Provide feedback on:
            - Code quality and adherence to best practices
            - Potential bugs or edge cases
            - Performance considerations
            - Security implications
            - Suggestions for improvement

            Since this PR touches critical source code paths, please be thorough
            in your review and provide inline comments where appropriate.
